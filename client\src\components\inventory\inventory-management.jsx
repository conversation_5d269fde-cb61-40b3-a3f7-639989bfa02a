import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import {
  Search,
  Package,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Edit,
  Trash2,
  Eye,
} from "lucide-react";
import inventoryService from "@/services/inventory-service";

const InventoryManagement = () => {
  const [products, setProducts] = useState([]);
  const [statistics, setStatistics] = useState({});
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [pagination, setPagination] = useState({
    page: 0,
    size: 12,
    totalElements: 0,
    totalPages: 1,
  });

  // Modal states
  const [editPriceModal, setEditPriceModal] = useState({
    open: false,
    product: null,
    newPrice: "",
  });
  const [deleteModal, setDeleteModal] = useState({
    open: false,
    product: null,
  });

  useEffect(() => {
    fetchProducts();
    fetchStatistics();
  }, [pagination.page, pagination.size]);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (pagination.page === 0) {
        fetchProducts();
      } else {
        setPagination(prev => ({ ...prev, page: 0 }));
      }
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await inventoryService.getInventoryProducts({
        page: pagination.page,
        size: pagination.size,
        sortBy: "label",
        sortDir: "asc",
        search: searchTerm.trim() || undefined,
      });

      if (response.success) {
        const products = Array.isArray(response.data.content) ? response.data.content : [];
        setProducts(products);
        setPagination(prev => ({
          ...prev,
          totalElements: response.data.totalElements || 0,
          totalPages: response.data.totalPages || 1,
        }));
      } else {
        console.error("Failed to fetch products:", response.message);
        setProducts([]);
        setPagination(prev => ({
          ...prev,
          totalElements: 0,
          totalPages: 1,
        }));
        toast.error(response.message || "Failed to load inventory products");
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      setProducts([]);
      setPagination(prev => ({
        ...prev,
        totalElements: 0,
        totalPages: 1,
      }));
      toast.error("Failed to load inventory products");
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await inventoryService.getInventoryStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error("Error fetching statistics:", error);
    }
  };

  const formatCurrency = (amount) => {
    const formattedNumber = Number(amount).toLocaleString("en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
    return formattedNumber.replace(/,/g, " ") + " MAD";
  };

  const handleEditPrice = (product) => {
    setEditPriceModal({
      open: true,
      product,
      newPrice: product.sellingPrice.toString(),
    });
  };

  const handleUpdatePrice = async () => {
    try {
      const response = await inventoryService.updateProductPrice(
        editPriceModal.product.id,
        editPriceModal.newPrice
      );

      if (response.success) {
        toast.success("Product price updated successfully");
        setEditPriceModal({ open: false, product: null, newPrice: "" });
        fetchProducts();
        fetchStatistics();
      } else {
        toast.error(response.message || "Failed to update price");
      }
    } catch (error) {
      console.error("Error updating price:", error);
      toast.error("Failed to update price");
    }
  };

  const handleDeleteProduct = async () => {
    try {
      const response = await inventoryService.deleteInventoryProduct(deleteModal.product.id);

      if (response.success) {
        toast.success("Product removed from inventory successfully");
        setDeleteModal({ open: false, product: null });
        fetchProducts();
        fetchStatistics();
      } else {
        toast.error(response.message || "Failed to delete product");
      }
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error("Failed to delete product");
    }
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Inventory Management</h1>
          <p className="text-muted-foreground">
            Manage your company's product inventory
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.totalProducts || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Stock</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {statistics.inStockProducts || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {statistics.outOfStockProducts || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(statistics.totalInventoryValue || 0).split(".")[0]}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {/* Products Grid */}
      {loading ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : products.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Package className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No products found</h3>
            <p className="text-muted-foreground text-center">
              {searchTerm ? "No products match your search criteria." : "No products in inventory yet."}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {products.map((product) => (
            <Card key={product.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="space-y-3">
                  {/* Product Image */}
                  <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                    {product.imageUrl ? (
                      <img
                        src={product.imageUrl}
                        alt={product.label}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Package className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                  </div>

                  {/* Product Info */}
                  <div>
                    <h3 className="font-semibold text-sm truncate" title={product.label}>
                      {product.label}
                    </h3>
                    <p className="text-xs text-muted-foreground truncate" title={product.brand}>
                      {product.brand || "No brand"}
                    </p>
                  </div>

                  {/* Stock and Price */}
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-muted-foreground">Stock:</span>
                      <Badge variant={product.stockQuantity > 0 ? "default" : "destructive"}>
                        {product.stockQuantity}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-muted-foreground">Price:</span>
                      <span className="font-semibold text-sm">
                        {formatCurrency(product.sellingPrice)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-muted-foreground">Margin:</span>
                      <span className="text-xs font-medium text-green-600">
                        {product.margin?.toFixed(1) || 0}%
                      </span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditPrice(product)}
                      className="flex-1"
                    >
                      <Edit className="h-3 w-3 mr-1" />
                      Edit Price
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setDeleteModal({ open: true, product })}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={pagination.page === 0}
          >
            Previous
          </Button>
          <span className="text-sm text-muted-foreground">
            Page {pagination.page + 1} of {pagination.totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={pagination.page >= pagination.totalPages - 1}
          >
            Next
          </Button>
        </div>
      )}

      {/* Edit Price Modal */}
      <Dialog open={editPriceModal.open} onOpenChange={(open) => 
        setEditPriceModal(prev => ({ ...prev, open }))}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Product Price</DialogTitle>
            <DialogDescription>
              Update the selling price for "{editPriceModal.product?.label}"
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="newPrice">New Selling Price (MAD)</Label>
              <Input
                id="newPrice"
                type="number"
                step="0.01"
                min="0"
                value={editPriceModal.newPrice}
                onChange={(e) => setEditPriceModal(prev => ({ 
                  ...prev, 
                  newPrice: e.target.value 
                }))}
                placeholder="Enter new price"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setEditPriceModal({ open: false, product: null, newPrice: "" })}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdatePrice}>
              Update Price
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={deleteModal.open} onOpenChange={(open) => 
        setDeleteModal(prev => ({ ...prev, open }))}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Product from Inventory</DialogTitle>
            <DialogDescription>
              Are you sure you want to remove "{deleteModal.product?.label}" from inventory?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteModal({ open: false, product: null })}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteProduct}>
              Delete Product
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default InventoryManagement;
